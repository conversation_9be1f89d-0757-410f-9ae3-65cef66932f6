import { NextRequest, NextResponse } from 'next/server';
import { applySecurityHeaders, getSecurityConfig } from '@/lib/security-headers';

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  try {
    // Apply security headers to all responses
    const response = NextResponse.next();
    const securityConfig = getSecurityConfig();
    applySecurityHeaders(response, securityConfig);

    // Handle CORS for API routes - Critical for OAuth flow
    if (pathname.startsWith('/api/')) {
      const origin = request.headers.get('origin');
      const allowedOrigins = [
        'http://localhost:3001', // Member portal
        'http://localhost:3002', // This app
      ];

      // Check if origin is allowed
      if (origin && allowedOrigins.includes(origin)) {
        response.headers.set('Access-Control-Allow-Origin', origin);
      } else if (!origin) {
        // Same-origin requests (no origin header)
        response.headers.set('Access-Control-Allow-Origin', 'http://localhost:3002');
      }

      response.headers.set('Access-Control-Allow-Credentials', 'true');
      response.headers.set('Access-Control-Allow-Methods', 'GET,POST,PUT,PATCH,DELETE,OPTIONS');

      // Reflect requested headers to satisfy preflight (includes Next.js internal headers)
      const requestedHeaders = request.headers.get('access-control-request-headers');
      const defaultAllowed = 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cookie, Set-Cookie, X-CSRF-Token, next-router-state-tree, Next-Router-State-Tree, next-url, Next-Url, rsc, RSC';
      response.headers.set('Access-Control-Allow-Headers', requestedHeaders || defaultAllowed);

      // Expose Set-Cookie so clients can read it when needed
      response.headers.set('Access-Control-Expose-Headers', 'Set-Cookie');

      // Help caches vary by origin and requested headers
      response.headers.append('Vary', 'Origin');
      response.headers.append('Vary', 'Access-Control-Request-Headers');

      // Handle preflight requests
      if (request.method === 'OPTIONS') {
        return new NextResponse(null, { status: 204, headers: response.headers });
      }
    }

    // Let NextAuth handle authentication - middleware only handles CORS and basic security
    
    return response;
  } catch (error) {
    // Simple error handling for edge runtime
    console.error('Middleware error:', error);
    return NextResponse.next();
  }
}

export const config = {
  matcher: [
    // Handle API routes for CORS, but EXCLUDE ALL NextAuth routes to avoid interfering with auth cookies
    // NextAuth handles its own CORS and cookie management
    '/api/((?!auth/).*)',
  ],
};