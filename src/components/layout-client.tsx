'use client';

import { Session } from "next-auth";
import { SessionProvider, useSession, signIn, signOut } from "next-auth/react";
import NotificationBell from "@/components/notification-bell";
import PWAInstallPrompt from "@/components/PWAInstallPrompt";
import { ThemeProvider } from "@/contexts/ThemeContext";
import Link from "next/link";
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { FilmIcon, HomeIcon, ChartBarIcon, ArrowRightOnRectangleIcon, TrophyIcon, UserCircleIcon } from '@heroicons/react/24/outline';

function MainNavigation() {
  const { data: session } = useSession();
  
  return (
    <nav className="ml-12 flex space-x-1">
      <Link href="/" className="text-gray-300 hover:bg-white/10 hover:text-white inline-flex items-center px-4 py-2 rounded-lg text-base font-medium transition-all duration-200">
        <HomeIcon className="h-5 w-5 mr-2" />
        Home
      </Link>
      <Link href="/leaderboard" className="text-gray-300 hover:bg-white/10 hover:text-white inline-flex items-center px-4 py-2 rounded-lg text-base font-medium transition-all duration-200">
        <TrophyIcon className="h-5 w-5 mr-2" />
        Leaderboard
      </Link>
      {session && (
        <Link href="/dashboard" className="text-gray-300 hover:bg-white/10 hover:text-white inline-flex items-center px-4 py-2 rounded-lg text-base font-medium transition-all duration-200">
          <ChartBarIcon className="h-5 w-5 mr-2" />
          Dashboard
        </Link>
      )}
    </nav>
  );
}

function AuthStatus() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const pathname = usePathname();
  const [isClient, setIsClient] = useState(false);

  // Redirect to dashboard if already logged in and on auth pages
  useEffect(() => {
    setIsClient(true);
    if (status === 'authenticated' && pathname && ['/auth/signin', '/login', '/register'].includes(pathname)) {
      // Add a 3-second delay to allow viewing any error messages
      setTimeout(() => {
        router.push('/dashboard');
      }, 3000);
    }
  }, [status, pathname, router]);

  // Don't render auth status on server to prevent hydration mismatch
  if (!isClient) {
    return (
      <div className="flex items-center space-x-2">
        <div className="h-8 w-8 rounded-full bg-gray-700"></div>
      </div>
    );
  }

  if (status === 'loading') {
    return (
      <div className="flex items-center space-x-2">
        <div className="h-8 w-8 rounded-full bg-gray-700 animate-pulse"></div>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-4">
      {!session ? (
        <button
          onClick={() => {
            // When signing in, redirect to the originally intended page or dashboard
            const callbackUrl = pathname && pathname !== '/' ? pathname : '/dashboard';

            // Use NextAuth's signIn function to trigger direct OAuth redirect
            signIn('member-portal', { callbackUrl });
          }}
          className="group flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-lg text-white font-medium hover:opacity-90 transition-opacity duration-200"
        >
          <span>Sign In</span>
          <ArrowRightOnRectangleIcon className="h-5 w-5 group-hover:translate-x-0.5 transition-transform duration-200" />
        </button>
      ) : (
        <div className="flex items-center space-x-4">
          <Link 
            href="/dashboard" 
            className="flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white font-medium transition-colors duration-200"
          >
            <UserCircleIcon className="h-5 w-5 mr-2" />
            <span>{session.user?.name || 'Dashboard'}</span>
          </Link>
          <button
            onClick={() => {
              // Use NextAuth's built-in signOut function for OAuth logout
              // This will trigger the signOut event in auth-config.ts which handles:
              // - OAuth token invalidation with remote server
              // - Custom session manager cleanup
              // - Cookie clearing
              // - Proper redirect
              signOut({
                callbackUrl: '/',
                redirect: true
              });
            }}
            className="text-gray-300 hover:bg-white/10 hover:text-white inline-flex items-center px-4 py-2 rounded-lg text-base font-medium transition-all duration-200"
            type="button"
          >
            <ArrowRightOnRectangleIcon className="h-5 w-5 mr-2" />
            Sign Out
          </button>
        </div>
      )}
      <NotificationBell />
    </div>
  );
}

export default function LayoutClient({
  children,
  session,
}: {
  children: React.ReactNode;
  session: Session | null;
}) {
  return (
    <SessionProvider session={session}>
      <ThemeProvider>
        <div className="min-h-screen flex flex-col">
          <header className="glass-card border-b border-white/10 sticky top-0 z-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between h-20">
                <div className="flex items-center">
                  <Link href="/" className="flex-shrink-0 flex items-center space-x-3 group">
                    <div className="p-2 rounded-lg bg-gradient-to-br from-purple-600 to-cyan-500 shadow-lg group-hover:scale-105 transition-transform duration-200">
                      <FilmIcon className="h-8 w-8 text-white" />
                    </div>
                    <h1 className="text-2xl font-bold font-display bg-gradient-to-r from-purple-400 via-cyan-400 to-purple-400 bg-clip-text text-transparent" data-testid="site-title">
                      NWA Media
                    </h1>
                  </Link>
                  <MainNavigation />
                </div>
                <AuthStatus />
              </div>
            </div>
          </header>
          <main className="flex-grow">{children}</main>
          <footer className="glass-card border-t border-white/10 py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <p className="text-center text-sm text-gray-400">
                © {new Date().getFullYear()} NWA Media Promotion. Promoting Peace and Prosperity through Visual Media
              </p>
            </div>
          </footer>
          <PWAInstallPrompt />
        </div>
      </ThemeProvider>
    </SessionProvider>
  );
}
